import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import { getActivityLogs } from '../services/activityLogService';

export const getAllActivityLogs = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const userType = req.query.userType as string;
    const activityType = req.query.activityType as string;
    const search = req.query.search as string;
    const searchName = req.query.searchName as string;
    const searchEmail = req.query.searchEmail as string;
    const searchContact = req.query.searchContact as string;
    const searchActivity = req.query.searchActivity as string;

    const result = await getActivityLogs({
      page,
      limit,
      userType,
      activityType,
      search,
      searchName,
      searchEmail,
      searchContact,
      searchActivity,
    });

    return sendSuccess(res, result, 'Activity logs retrieved successfully');
  } catch (error: any) {
    console.error('Get activity logs error:', error);
    return sendError(res, `Failed to retrieve activity logs: ${error.message}`, 500);
  }
};


