[{"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx": "1", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx": "2", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx": "3", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx": "4", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx": "5", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx": "6", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx": "7", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx": "8", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx": "9", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx": "10", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx": "11", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx": "12", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx": "13", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx": "14", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx": "15", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx": "16", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx": "17", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx": "18", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx": "19", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx": "20", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx": "21", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx": "22", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx": "23", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx": "24", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx": "25", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx": "26", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx": "27", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx": "28", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx": "29", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx": "30", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx": "31", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx": "32", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx": "33", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx": "34", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx": "35", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx": "36", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx": "37", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx": "38", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx": "39", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx": "40", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx": "41", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx": "42", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx": "43", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx": "44", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx": "45", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx": "46", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx": "47", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx": "48", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx": "49", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx": "50", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx": "51", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx": "52", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx": "53", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx": "54", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx": "55", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx": "56", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx": "57", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx": "58", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx": "59", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx": "60", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx": "61", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx": "62", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx": "63", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx": "64", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx": "65", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx": "66", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx": "67", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx": "68", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx": "69", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx": "70", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx": "71", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx": "72", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx": "73", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx": "74", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx": "75", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx": "76", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx": "77", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx": "78", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx": "79", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx": "80", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx": "81", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx": "82", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx": "83", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx": "84", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx": "85", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx": "86", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx": "87", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx": "88", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx": "89", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx": "90", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx": "91", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts": "92", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts": "93", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts": "94", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts": "95", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts": "96", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts": "97", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts": "98", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts": "99", "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx": "100", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts": "101", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts": "102", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts": "103", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts": "104", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts": "105", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts": "106", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts": "107", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts": "108", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts": "109", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts": "110", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts": "111", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts": "112", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts": "113", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts": "114", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts": "115", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx": "116", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts": "117", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts": "118", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts": "119", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts": "120", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx": "121", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx": "122", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx": "123", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx": "124", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx": "125", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx": "126", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts": "127", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx": "128", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx": "129", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx": "130", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts": "131", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx": "132", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx": "133", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts": "134", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts": "135", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts": "136", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx": "137", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx": "138", "G:\\UEST\\uest_app\\uest-app\\client\\src\\utils\\pdfGenerator.ts": "139", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx": "140", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts": "141", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts": "142", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx": "143", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx": "144", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx": "145", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx": "146", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts": "147", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts": "148", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts": "149", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts": "150", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts": "151", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts": "152", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx": "153", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx": "154", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx": "155", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts": "156", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx": "157", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx": "158", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx": "159", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts": "160", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts": "161", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx": "162", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx": "163", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx": "164", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts": "165", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx": "166", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx": "167", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx": "168", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx": "169", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx": "170", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-otp\\page.tsx": "171", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badgedisplay.tsx": "172", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\streakcountdisplay.tsx": "173", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\totaluestcoin.tsx": "174", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\getStudentId.tsx": "175", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\LeaderboardUserApi.ts": "176", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mock-exam-resultApi.ts": "177", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mockExamStreakApi.ts": "178", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uestCoinTransctionApi.ts": "179", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamTerminationApi.ts": "180"}, {"size": 156, "mtime": 1751275811335, "results": "181", "hashOfConfig": "182"}, {"size": 4799, "mtime": 1751275807358, "results": "183", "hashOfConfig": "182"}, {"size": 5240, "mtime": 1751275802728, "results": "184", "hashOfConfig": "182"}, {"size": 29458, "mtime": 1751625232828, "results": "185", "hashOfConfig": "182"}, {"size": 3171, "mtime": 1747109292602, "results": "186", "hashOfConfig": "182"}, {"size": 4002, "mtime": 1747109292604, "results": "187", "hashOfConfig": "182"}, {"size": 226, "mtime": 1751024271963, "results": "188", "hashOfConfig": "182"}, {"size": 5928, "mtime": 1751024275820, "results": "189", "hashOfConfig": "182"}, {"size": 2482, "mtime": 1752596826384, "results": "190", "hashOfConfig": "182"}, {"size": 5384, "mtime": 1747289688954, "results": "191", "hashOfConfig": "182"}, {"size": 3658, "mtime": 1747289688955, "results": "192", "hashOfConfig": "182"}, {"size": 674, "mtime": 1747289688955, "results": "193", "hashOfConfig": "182"}, {"size": 5932, "mtime": 1752465858736, "results": "194", "hashOfConfig": "182"}, {"size": 114, "mtime": 1752596842332, "results": "195", "hashOfConfig": "182"}, {"size": 597, "mtime": 1752596848563, "results": "196", "hashOfConfig": "182"}, {"size": 13660, "mtime": 1749201002331, "results": "197", "hashOfConfig": "182"}, {"size": 550, "mtime": 1747289688983, "results": "198", "hashOfConfig": "182"}, {"size": 1623, "mtime": 1747289688984, "results": "199", "hashOfConfig": "182"}, {"size": 3787, "mtime": 1747990267735, "results": "200", "hashOfConfig": "182"}, {"size": 516, "mtime": 1747289689001, "results": "201", "hashOfConfig": "182"}, {"size": 17426, "mtime": 1752465858750, "results": "202", "hashOfConfig": "182"}, {"size": 551, "mtime": 1747289689003, "results": "203", "hashOfConfig": "182"}, {"size": 14780, "mtime": 1747990267862, "results": "204", "hashOfConfig": "182"}, {"size": 557, "mtime": 1747289689005, "results": "205", "hashOfConfig": "182"}, {"size": 4900, "mtime": 1751625232913, "results": "206", "hashOfConfig": "182"}, {"size": 451, "mtime": 1747289689007, "results": "207", "hashOfConfig": "182"}, {"size": 514, "mtime": 1747289689008, "results": "208", "hashOfConfig": "182"}, {"size": 9870, "mtime": 1748962020705, "results": "209", "hashOfConfig": "182"}, {"size": 9250, "mtime": 1749206244199, "results": "210", "hashOfConfig": "182"}, {"size": 578, "mtime": 1747289689027, "results": "211", "hashOfConfig": "182"}, {"size": 20216, "mtime": 1752486686589, "results": "212", "hashOfConfig": "182"}, {"size": 17836, "mtime": 1752465858733, "results": "213", "hashOfConfig": "182"}, {"size": 13775, "mtime": 1749201002401, "results": "214", "hashOfConfig": "182"}, {"size": 1778, "mtime": 1752570168084, "results": "215", "hashOfConfig": "182"}, {"size": 30747, "mtime": 1752465859132, "results": "216", "hashOfConfig": "182"}, {"size": 531, "mtime": 1747109267499, "results": "217", "hashOfConfig": "182"}, {"size": 22138, "mtime": 1753156971748, "results": "218", "hashOfConfig": "182"}, {"size": 10121, "mtime": 1747624459676, "results": "219", "hashOfConfig": "182"}, {"size": 6100, "mtime": 1747109267569, "results": "220", "hashOfConfig": "182"}, {"size": 354, "mtime": 1747109267605, "results": "221", "hashOfConfig": "182"}, {"size": 4841, "mtime": 1747109267602, "results": "222", "hashOfConfig": "182"}, {"size": 14852, "mtime": 1747109267661, "results": "223", "hashOfConfig": "182"}, {"size": 4990, "mtime": 1749722967811, "results": "224", "hashOfConfig": "182"}, {"size": 4724, "mtime": 1749722967814, "results": "225", "hashOfConfig": "182"}, {"size": 35295, "mtime": 1752465859472, "results": "226", "hashOfConfig": "182"}, {"size": 15866, "mtime": 1749486774681, "results": "227", "hashOfConfig": "182"}, {"size": 1070, "mtime": 1752489573454, "results": "228", "hashOfConfig": "182"}, {"size": 27384, "mtime": 1752489573456, "results": "229", "hashOfConfig": "182"}, {"size": 2942, "mtime": 1747289689132, "results": "230", "hashOfConfig": "182"}, {"size": 2594, "mtime": 1747109292536, "results": "231", "hashOfConfig": "182"}, {"size": 2908, "mtime": 1747624459651, "results": "232", "hashOfConfig": "182"}, {"size": 695, "mtime": 1749485471880, "results": "233", "hashOfConfig": "182"}, {"size": 4253, "mtime": 1747289688716, "results": "234", "hashOfConfig": "182"}, {"size": 9141, "mtime": 1747289688734, "results": "235", "hashOfConfig": "182"}, {"size": 5534, "mtime": 1751625232647, "results": "236", "hashOfConfig": "182"}, {"size": 35697, "mtime": 1753156971433, "results": "237", "hashOfConfig": "182"}, {"size": 1582, "mtime": 1747109267153, "results": "238", "hashOfConfig": "182"}, {"size": 3175, "mtime": 1747289688746, "results": "239", "hashOfConfig": "182"}, {"size": 18291, "mtime": 1747624459652, "results": "240", "hashOfConfig": "182"}, {"size": 9254, "mtime": 1752465858567, "results": "241", "hashOfConfig": "182"}, {"size": 6818, "mtime": 1747654911853, "results": "242", "hashOfConfig": "182"}, {"size": 2125, "mtime": 1747109268031, "results": "243", "hashOfConfig": "182"}, {"size": 4021, "mtime": 1747289689134, "results": "244", "hashOfConfig": "182"}, {"size": 1090, "mtime": 1747109268032, "results": "245", "hashOfConfig": "182"}, {"size": 1634, "mtime": 1747109268033, "results": "246", "hashOfConfig": "182"}, {"size": 2158, "mtime": 1747109268035, "results": "247", "hashOfConfig": "182"}, {"size": 6645, "mtime": 1748363529790, "results": "248", "hashOfConfig": "182"}, {"size": 2003, "mtime": 1747109268037, "results": "249", "hashOfConfig": "182"}, {"size": 1258, "mtime": 1747109268043, "results": "250", "hashOfConfig": "182"}, {"size": 833, "mtime": 1747289689135, "results": "251", "hashOfConfig": "182"}, {"size": 0, "mtime": 1744777321785, "results": "252", "hashOfConfig": "182"}, {"size": 1166, "mtime": 1747624459679, "results": "253", "hashOfConfig": "182"}, {"size": 3914, "mtime": 1747109268044, "results": "254", "hashOfConfig": "182"}, {"size": 8541, "mtime": 1747289689136, "results": "255", "hashOfConfig": "182"}, {"size": 3871, "mtime": 1747109268047, "results": "256", "hashOfConfig": "182"}, {"size": 992, "mtime": 1747109268048, "results": "257", "hashOfConfig": "182"}, {"size": 634, "mtime": 1747109268051, "results": "258", "hashOfConfig": "182"}, {"size": 4268, "mtime": 1753156971755, "results": "259", "hashOfConfig": "182"}, {"size": 2860, "mtime": 1747289689149, "results": "260", "hashOfConfig": "182"}, {"size": 1680, "mtime": 1747109268054, "results": "261", "hashOfConfig": "182"}, {"size": 750, "mtime": 1747109268056, "results": "262", "hashOfConfig": "182"}, {"size": 6382, "mtime": 1747109268057, "results": "263", "hashOfConfig": "182"}, {"size": 738, "mtime": 1747109268059, "results": "264", "hashOfConfig": "182"}, {"size": 4229, "mtime": 1747289689150, "results": "265", "hashOfConfig": "182"}, {"size": 22359, "mtime": 1747289689157, "results": "266", "hashOfConfig": "182"}, {"size": 292, "mtime": 1747109268060, "results": "267", "hashOfConfig": "182"}, {"size": 596, "mtime": 1747109268061, "results": "268", "hashOfConfig": "182"}, {"size": 2564, "mtime": 1747289689158, "results": "269", "hashOfConfig": "182"}, {"size": 2016, "mtime": 1747109268062, "results": "270", "hashOfConfig": "182"}, {"size": 781, "mtime": 1747109268063, "results": "271", "hashOfConfig": "182"}, {"size": 1952, "mtime": 1747289689159, "results": "272", "hashOfConfig": "182"}, {"size": 584, "mtime": 1749468144347, "results": "273", "hashOfConfig": "182"}, {"size": 7273, "mtime": 1747109268066, "results": "274", "hashOfConfig": "182"}, {"size": 1380, "mtime": 1752561698912, "results": "275", "hashOfConfig": "182"}, {"size": 188, "mtime": 1747109268072, "results": "276", "hashOfConfig": "182"}, {"size": 2293, "mtime": 1752465860247, "results": "277", "hashOfConfig": "182"}, {"size": 8637, "mtime": 1752684636347, "results": "278", "hashOfConfig": "182"}, {"size": 465, "mtime": 1747109268078, "results": "279", "hashOfConfig": "182"}, {"size": 824, "mtime": 1747289689165, "results": "280", "hashOfConfig": "182"}, {"size": 310, "mtime": 1747109267096, "results": "281", "hashOfConfig": "182"}, {"size": 2032, "mtime": 1752465860837, "results": "282", "hashOfConfig": "182"}, {"size": 3382, "mtime": 1751255662795, "results": "283", "hashOfConfig": "182"}, {"size": 843, "mtime": 1747109292640, "results": "284", "hashOfConfig": "182"}, {"size": 1282, "mtime": 1751625233134, "results": "285", "hashOfConfig": "182"}, {"size": 2363, "mtime": 1748260878717, "results": "286", "hashOfConfig": "182"}, {"size": 1193, "mtime": 1748964660194, "results": "287", "hashOfConfig": "182"}, {"size": 438, "mtime": 1747109268089, "results": "288", "hashOfConfig": "182"}, {"size": 508, "mtime": 1747109268089, "results": "289", "hashOfConfig": "182"}, {"size": 1564, "mtime": 1747624459698, "results": "290", "hashOfConfig": "182"}, {"size": 2464, "mtime": 1753182055485, "results": "291", "hashOfConfig": "182"}, {"size": 3017, "mtime": 1747289689201, "results": "292", "hashOfConfig": "182"}, {"size": 1144, "mtime": 1747109268090, "results": "293", "hashOfConfig": "182"}, {"size": 414, "mtime": 1747109268091, "results": "294", "hashOfConfig": "182"}, {"size": 484, "mtime": 1747109268092, "results": "295", "hashOfConfig": "182"}, {"size": 590, "mtime": 1747883078088, "results": "296", "hashOfConfig": "182"}, {"size": 232, "mtime": 1747109268096, "results": "297", "hashOfConfig": "182"}, {"size": 1095, "mtime": 1747109268097, "results": "298", "hashOfConfig": "182"}, {"size": 1516, "mtime": 1750649654430, "results": "299", "hashOfConfig": "182"}, {"size": 1157, "mtime": 1752644594433, "results": "300", "hashOfConfig": "182"}, {"size": 458, "mtime": 1749201002646, "results": "301", "hashOfConfig": "182"}, {"size": 10094, "mtime": 1751024283211, "results": "302", "hashOfConfig": "182"}, {"size": 26618, "mtime": 1752465858731, "results": "303", "hashOfConfig": "182"}, {"size": 28459, "mtime": 1751625232977, "results": "304", "hashOfConfig": "182"}, {"size": 27024, "mtime": 1752465859199, "results": "305", "hashOfConfig": "182"}, {"size": 3193, "mtime": 1748962020700, "results": "306", "hashOfConfig": "182"}, {"size": 1643, "mtime": 1747624459680, "results": "307", "hashOfConfig": "182"}, {"size": 3539, "mtime": 1748260878719, "results": "308", "hashOfConfig": "182"}, {"size": 23652, "mtime": 1753156971753, "results": "309", "hashOfConfig": "182"}, {"size": 5982, "mtime": 1750070278915, "results": "310", "hashOfConfig": "182"}, {"size": 433, "mtime": 1749485552367, "results": "311", "hashOfConfig": "182"}, {"size": 466, "mtime": 1747797160907, "results": "312", "hashOfConfig": "182"}, {"size": 53551, "mtime": 1753156932331, "results": "313", "hashOfConfig": "182"}, {"size": 1825, "mtime": 1750070278881, "results": "314", "hashOfConfig": "182"}, {"size": 356, "mtime": 1747883078087, "results": "315", "hashOfConfig": "182"}, {"size": 4161, "mtime": 1753156932333, "results": "316", "hashOfConfig": "182"}, {"size": 2286, "mtime": 1752919194885, "results": "317", "hashOfConfig": "182"}, {"size": 16992, "mtime": 1751625232990, "results": "318", "hashOfConfig": "182"}, {"size": 17122, "mtime": 1748768935829, "results": "319", "hashOfConfig": "182"}, {"size": 8676, "mtime": 1749015983232, "results": "320", "hashOfConfig": "182"}, {"size": 977, "mtime": 1748768935828, "results": "321", "hashOfConfig": "182"}, {"size": 706, "mtime": 1748768935833, "results": "322", "hashOfConfig": "182"}, {"size": 523, "mtime": 1749201002641, "results": "323", "hashOfConfig": "182"}, {"size": 4714, "mtime": 1752465858945, "results": "324", "hashOfConfig": "182"}, {"size": 218, "mtime": 1752465859129, "results": "325", "hashOfConfig": "182"}, {"size": 1279, "mtime": 1749486774674, "results": "326", "hashOfConfig": "182"}, {"size": 40948, "mtime": 1753156971750, "results": "327", "hashOfConfig": "182"}, {"size": 962, "mtime": 1750070278918, "results": "328", "hashOfConfig": "182"}, {"size": 404, "mtime": 1749486774735, "results": "329", "hashOfConfig": "182"}, {"size": 499, "mtime": 1752465862527, "results": "330", "hashOfConfig": "182"}, {"size": 535, "mtime": 1749885108597, "results": "331", "hashOfConfig": "182"}, {"size": 2278, "mtime": 1749486774737, "results": "332", "hashOfConfig": "182"}, {"size": 460, "mtime": 1749486774737, "results": "333", "hashOfConfig": "182"}, {"size": 905, "mtime": 1752465858734, "results": "334", "hashOfConfig": "182"}, {"size": 2443, "mtime": 1751276652402, "results": "335", "hashOfConfig": "182"}, {"size": 58450, "mtime": 1753156971616, "results": "336", "hashOfConfig": "182"}, {"size": 1171, "mtime": 1751276652417, "results": "337", "hashOfConfig": "182"}, {"size": 8203, "mtime": 1752651150682, "results": "338", "hashOfConfig": "182"}, {"size": 535, "mtime": 1750649654200, "results": "339", "hashOfConfig": "182"}, {"size": 5275, "mtime": 1750649654371, "results": "340", "hashOfConfig": "182"}, {"size": 964, "mtime": 1751276652418, "results": "341", "hashOfConfig": "182"}, {"size": 841, "mtime": 1750649654428, "results": "342", "hashOfConfig": "182"}, {"size": 1293, "mtime": 1751625233132, "results": "343", "hashOfConfig": "182"}, {"size": 22034, "mtime": 1753156971726, "results": "344", "hashOfConfig": "182"}, {"size": 9980, "mtime": 1753156971472, "results": "345", "hashOfConfig": "182"}, {"size": 3319, "mtime": 1751625233498, "results": "346", "hashOfConfig": "182"}, {"size": 14732, "mtime": 1752465858569, "results": "347", "hashOfConfig": "182"}, {"size": 17837, "mtime": 1753156971642, "results": "348", "hashOfConfig": "182"}, {"size": 11665, "mtime": 1752465858933, "results": "349", "hashOfConfig": "182"}, {"size": 43223, "mtime": 1752465858936, "results": "350", "hashOfConfig": "182"}, {"size": 13760, "mtime": 1752465859165, "results": "351", "hashOfConfig": "182"}, {"size": 14090, "mtime": 1752465859848, "results": "352", "hashOfConfig": "182"}, {"size": 1336, "mtime": 1752465859850, "results": "353", "hashOfConfig": "182"}, {"size": 1051, "mtime": 1752465859906, "results": "354", "hashOfConfig": "182"}, {"size": 1196, "mtime": 1752465859911, "results": "355", "hashOfConfig": "182"}, {"size": 1101, "mtime": 1752465860219, "results": "356", "hashOfConfig": "182"}, {"size": 680, "mtime": 1752465860839, "results": "357", "hashOfConfig": "182"}, {"size": 1114, "mtime": 1752465861652, "results": "358", "hashOfConfig": "182"}, {"size": 1056, "mtime": 1752465861807, "results": "359", "hashOfConfig": "182"}, {"size": 951, "mtime": 1752465862526, "results": "360", "hashOfConfig": "182"}, {"size": 941, "mtime": 1752465862528, "results": "361", "hashOfConfig": "182"}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uj1650", {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx", ["902"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx", ["903"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx", [], ["904"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx", [], ["905"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts", [], ["906"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts", [], ["907"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts", ["908"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx", [], ["909", "910"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\utils\\pdfGenerator.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx", ["911", "912", "913", "914", "915", "916", "917"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx", ["918"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx", ["919"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx", ["920", "921", "922", "923", "924"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-otp\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badgedisplay.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\streakcountdisplay.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\totaluestcoin.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\getStudentId.tsx", ["925"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\LeaderboardUserApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mock-exam-resultApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mockExamStreakApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uestCoinTransctionApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamTerminationApi.ts", [], [], {"ruleId": "926", "severity": 1, "message": "927", "line": 233, "column": 6, "nodeType": "928", "endLine": 233, "endColumn": 8, "suggestions": "929"}, {"ruleId": "926", "severity": 1, "message": "930", "line": 100, "column": 6, "nodeType": "928", "endLine": 100, "endColumn": 62, "suggestions": "931"}, {"ruleId": "926", "severity": 1, "message": "932", "line": 258, "column": 6, "nodeType": "928", "endLine": 258, "endColumn": 47, "suggestions": "933", "suppressions": "934"}, {"ruleId": "926", "severity": 1, "message": "935", "line": 21, "column": 6, "nodeType": "928", "endLine": 21, "endColumn": 17, "suggestions": "936", "suppressions": "937"}, {"ruleId": "926", "severity": 1, "message": "938", "line": 193, "column": 6, "nodeType": "928", "endLine": 202, "endColumn": 4, "suggestions": "939", "suppressions": "940"}, {"ruleId": "941", "severity": 2, "message": "942", "line": 44, "column": 15, "nodeType": "943", "messageId": "944", "endLine": 44, "endColumn": 22, "suppressions": "945"}, {"ruleId": "946", "severity": 2, "message": "947", "line": 56, "column": 12, "nodeType": null, "messageId": "948", "endLine": 56, "endColumn": 17}, {"ruleId": "926", "severity": 1, "message": "949", "line": 206, "column": 6, "nodeType": "928", "endLine": 206, "endColumn": 8, "suggestions": "950", "suppressions": "951"}, {"ruleId": "926", "severity": 1, "message": "952", "line": 211, "column": 6, "nodeType": "928", "endLine": 211, "endColumn": 42, "suggestions": "953", "suppressions": "954"}, {"ruleId": "926", "severity": 1, "message": "955", "line": 112, "column": 6, "nodeType": "928", "endLine": 112, "endColumn": 28, "suggestions": "956"}, {"ruleId": "926", "severity": 1, "message": "955", "line": 128, "column": 6, "nodeType": "928", "endLine": 128, "endColumn": 28, "suggestions": "957"}, {"ruleId": "926", "severity": 1, "message": "958", "line": 228, "column": 6, "nodeType": "928", "endLine": 228, "endColumn": 77, "suggestions": "959"}, {"ruleId": "926", "severity": 1, "message": "960", "line": 338, "column": 6, "nodeType": "928", "endLine": 338, "endColumn": 132, "suggestions": "961"}, {"ruleId": "926", "severity": 1, "message": "962", "line": 383, "column": 6, "nodeType": "928", "endLine": 383, "endColumn": 45, "suggestions": "963"}, {"ruleId": "926", "severity": 1, "message": "962", "line": 392, "column": 6, "nodeType": "928", "endLine": 392, "endColumn": 45, "suggestions": "964"}, {"ruleId": "926", "severity": 1, "message": "965", "line": 524, "column": 5, "nodeType": "928", "endLine": 524, "endColumn": 112, "suggestions": "966"}, {"ruleId": "926", "severity": 1, "message": "967", "line": 228, "column": 8, "nodeType": "928", "endLine": 228, "endColumn": 82, "suggestions": "968"}, {"ruleId": "926", "severity": 1, "message": "969", "line": 150, "column": 6, "nodeType": "928", "endLine": 150, "endColumn": 16, "suggestions": "970"}, {"ruleId": "926", "severity": 1, "message": "955", "line": 106, "column": 6, "nodeType": "928", "endLine": 106, "endColumn": 17, "suggestions": "971"}, {"ruleId": "926", "severity": 1, "message": "962", "line": 299, "column": 6, "nodeType": "928", "endLine": 299, "endColumn": 66, "suggestions": "972"}, {"ruleId": "926", "severity": 1, "message": "973", "line": 412, "column": 6, "nodeType": "928", "endLine": 412, "endColumn": 34, "suggestions": "974"}, {"ruleId": "926", "severity": 1, "message": "962", "line": 420, "column": 6, "nodeType": "928", "endLine": 420, "endColumn": 34, "suggestions": "975"}, {"ruleId": "926", "severity": 1, "message": "976", "line": 554, "column": 5, "nodeType": "928", "endLine": 554, "endColumn": 124, "suggestions": "977"}, {"ruleId": "926", "severity": 1, "message": "978", "line": 34, "column": 6, "nodeType": "928", "endLine": 34, "endColumn": 20, "suggestions": "979"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", "ArrayExpression", ["980"], "React Hook useEffect has a missing dependency: 'exam.duration'. Either include it or remove the dependency array.", ["981"], "React Hook useEffect has missing dependencies: 'fetchNearbyTutors' and 'fetchTutors'. Either include them or remove the dependency array.", ["982"], ["983"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["984"], ["985"], "React Hook useEffect has missing dependencies: 'enterFullscreen' and 'isFullscreen'. Either include them or remove the dependency array.", ["986"], ["987"], "prefer-const", "'minutes' is never reassigned. Use 'const' instead.", "Identifier", "useConst", ["988"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchConstants'. Either include it or remove the dependency array.", ["989"], ["990"], "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["991"], ["992"], "React Hook useEffect has a missing dependency: 'initializeViolationCounts'. Either include it or remove the dependency array.", ["993"], ["994"], "React Hook useCallback has missing dependencies: 'isCameraReady', 'isSubmitted', and 'router'. Either include them or remove the dependency array.", ["995"], "React Hook useEffect has a missing dependency: 'userAnswers'. Either include it or remove the dependency array.", ["996"], "React Hook useEffect has a missing dependency: 'exitFullScreen'. Either include it or remove the dependency array.", ["997"], ["998"], "React Hook useCallback has unnecessary dependencies: 'examIdStr' and 'studentId'. Either exclude them or remove the dependency array.", ["999"], "React Hook useEffect has missing dependencies: 'currentRoomId', 'offlineMessageUsers', 'selectedUserId', 'socketPath', and 'socketUrl'. Either include them or remove the dependency array.", ["1000"], "React Hook useCallback has a missing dependency: 'user'. Either include it or remove the dependency array.", ["1001"], ["1002"], ["1003"], "React Hook useEffect has missing dependencies: 'exitFullScreen' and 'showTermination'. Either include them or remove the dependency array.", ["1004"], ["1005"], "React Hook useCallback has an unnecessary dependency: 'studentId'. Either exclude it or remove the dependency array.", ["1006"], "React Hook useEffect has a missing dependency: 'router'. Either include it or remove the dependency array.", ["1007"], {"desc": "1008", "fix": "1009"}, {"desc": "1010", "fix": "1011"}, {"desc": "1012", "fix": "1013"}, {"kind": "1014", "justification": "1015"}, {"desc": "1016", "fix": "1017"}, {"kind": "1014", "justification": "1015"}, {"desc": "1018", "fix": "1019"}, {"kind": "1014", "justification": "1015"}, {"kind": "1014", "justification": "1015"}, {"desc": "1020", "fix": "1021"}, {"kind": "1014", "justification": "1015"}, {"desc": "1022", "fix": "1023"}, {"kind": "1014", "justification": "1015"}, {"desc": "1024", "fix": "1025"}, {"desc": "1024", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1029", "fix": "1030"}, {"desc": "1031", "fix": "1032"}, {"desc": "1033", "fix": "1034"}, {"desc": "1035", "fix": "1036"}, {"desc": "1037", "fix": "1038"}, {"desc": "1039", "fix": "1040"}, {"desc": "1041", "fix": "1042"}, {"desc": "1043", "fix": "1044"}, {"desc": "1045", "fix": "1046"}, {"desc": "1047", "fix": "1048"}, {"desc": "1049", "fix": "1050"}, {"desc": "1051", "fix": "1052"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1053", "text": "1054"}, "Update the dependencies array to be: [exam.start_date, exam.start_registration_date, exam.id, exam.duration]", {"range": "1055", "text": "1056"}, "Update the dependencies array to be: [page, useNearby, userLocation, distance, fetchNearbyTutors, fetchTutors]", {"range": "1057", "text": "1058"}, "directive", "", "Update the dependencies array to be: [authError, dispatch]", {"range": "1059", "text": "1060"}, "Update the dependencies array to be: [escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", {"range": "1061", "text": "1062"}, "Update the dependencies array to be: [fetchConstants]", {"range": "1063", "text": "1064"}, "Update the dependencies array to be: [currentPage, limit, filtersApplied, fetchQuestions]", {"range": "1065", "text": "1066"}, "Update the dependencies array to be: [studentId, examIdStr, initializeViolationCounts]", {"range": "1067", "text": "1068"}, {"range": "1069", "text": "1068"}, "Update the dependencies array to be: [isSubmitted, studentId, examIdStr, currentQuestionIndex, questions, isCameraReady, router, selectedAnswer]", {"range": "1070", "text": "1071"}, "Update the dependencies array to be: [currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", {"range": "1072", "text": "1073"}, "Update the dependencies array to be: [isQuizCompleted, studentId, examIdStr, exitFullScreen]", {"range": "1074", "text": "1075"}, "Update the dependencies array to be: [showTermination, studentId, examIdStr, exitFullScreen]", {"range": "1076", "text": "1077"}, "Update the dependencies array to be: [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", {"range": "1078", "text": "1079"}, "Update the dependencies array to be: [username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, socketUrl, socketPath, selectedUserId, offlineMessageUsers, currentRoomId]", {"range": "1080", "text": "1081"}, "Update the dependencies array to be: [user, userType]", {"range": "1082", "text": "1083"}, "Update the dependencies array to be: [initializeViolationCounts, studentId]", {"range": "1084", "text": "1085"}, "Update the dependencies array to be: [isQuizCompleted, studentId, calculateScore, calculateCoins, exitFullScreen]", {"range": "1086", "text": "1087"}, "Update the dependencies array to be: [exitFullScreen, isQuizCompleted, showTermination, studentId]", {"range": "1088", "text": "1089"}, "Update the dependencies array to be: [exitFullScreen, showTermination, studentId]", {"range": "1090", "text": "1091"}, "Update the dependencies array to be: [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]", {"range": "1092", "text": "1093"}, "Update the dependencies array to be: [router, searchParams]", {"range": "1094", "text": "1095"}, [7243, 7245], "[fetchCategories]", [3832, 3888], "[exam.start_date, exam.start_registration_date, exam.id, exam.duration]", [7250, 7291], "[page, useNearby, userLocation, distance, fetchNearbyTutors, fetchTutors]", [622, 633], "[auth<PERSON><PERSON><PERSON>, dispatch]", [6888, 7055], "[escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", [7258, 7260], "[fetchConstants]", [7403, 7439], "[currentPage, limit, filtersApplied, fetchQuestions]", [4187, 4209], "[studentId, examIdStr, initializeViolationCounts]", [4641, 4663], [7743, 7814], "[isSubmitted, studentId, examIdStr, currentQuestionIndex, questions, isCameraReady, router, selectedAnswer]", [11939, 12065], "[currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", [13940, 13979], "[isQuizCompleted, studentId, examIdStr, exitFullScreen]", [14270, 14309], "[showTermination, studentId, examIdStr, exitFullScreen]", [19640, 19747], "[isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", [9625, 9699], "[username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, socketUrl, socketPath, selectedUserId, offlineMessageUsers, currentRoomId]", [5157, 5167], "[user, userType]", [4060, 4071], "[initializeViolationCounts, studentId]", [10723, 10783], "[isQuizCompleted, studentId, calculateScore, calculateCoins, exitFullScreen]", [14905, 14933], "[exitFullScreen, isQuizCompleted, showTermination, studentId]", [15142, 15170], "[exitFullScreen, showTermination, studentId]", [20434, 20553], "[isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]", [1026, 1040], "[router, searchParams]"]