import prisma from '@/config/prismaClient';
import { UserType } from '@prisma/client';

interface GetActivityLogsParams {
  page: number;
  limit: number;
  userType?: string;
  activityType?: string;
  search?: string;
  searchName?: string;
  searchEmail?: string;
  searchContact?: string;
}

export const getActivityLogs = async (params: GetActivityLogsParams) => {
  const { page, limit, userType, activityType, search, searchName, searchEmail, searchContact } = params;
  const skip = (page - 1) * limit;

  const where: any = {};

  if (userType && Object.values(UserType).includes(userType as UserType)) {
    where.userType = userType as UserType;
  }

  if (activityType) {
    where.activityType = activityType;
  }

  const [logs, total] = await Promise.all([
    prisma.userActivityLog.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.userActivityLog.count({ where }),
  ]);

  const enrichedLogs = await Promise.all(
    logs.map(async (log) => {
      let userDetails = null;

      if (log.userType === UserType.STUDENT) {
        userDetails = await prisma.student.findUnique({
          where: { id: log.userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true,
          },
        });
      } else if (log.userType === UserType.CLASS) {
        userDetails = await prisma.classes.findUnique({
          where: { id: log.userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contactNo: true,
            className: true,
          },
        });
      }

      return {
        ...log,
        userDetails,
      };
    })
  );

  let filteredLogs = enrichedLogs;

  // Handle general search (backward compatibility)
  if (search) {
    const searchLower = search.toLowerCase();
    filteredLogs = enrichedLogs.filter((log) => {
      const userDetails = log.userDetails;
      if (!userDetails) return false;

      const fullName = `${userDetails.firstName} ${userDetails.lastName}`.toLowerCase();
      const email = userDetails.email?.toLowerCase() || '';

      let contact = '';
      if (log.userType === UserType.STUDENT && 'contact' in userDetails) {
        contact = userDetails.contact || '';
      } else if (log.userType === UserType.CLASS && 'contactNo' in userDetails) {
        contact = userDetails.contactNo || '';
      }

      return fullName.includes(searchLower) ||
        email.includes(searchLower) ||
        contact.toLowerCase().includes(searchLower);
    });
  }

  // Handle specific search parameters
  if (searchName || searchEmail || searchContact) {
    filteredLogs = filteredLogs.filter((log) => {
      const userDetails = log.userDetails;
      if (!userDetails) return false;

      let matches = true;

      if (searchName) {
        const fullName = `${userDetails.firstName} ${userDetails.lastName}`.toLowerCase();
        matches = matches && fullName.includes(searchName.toLowerCase());
      }

      if (searchEmail) {
        const email = userDetails.email?.toLowerCase() || '';
        matches = matches && email.includes(searchEmail.toLowerCase());
      }

      if (searchContact) {
        let contact = '';
        if (log.userType === UserType.STUDENT && 'contact' in userDetails) {
          contact = userDetails.contact || '';
        } else if (log.userType === UserType.CLASS && 'contactNo' in userDetails) {
          contact = userDetails.contactNo || '';
        }
        matches = matches && contact.toLowerCase().includes(searchContact.toLowerCase());
      }

      return matches;
    });
  }

  const hasAnySearch = search || searchName || searchEmail || searchContact;

  return {
    logs: hasAnySearch ? filteredLogs : enrichedLogs,
    pagination: {
      page,
      limit,
      total: hasAnySearch ? filteredLogs.length : total,
      totalPages: hasAnySearch ? Math.ceil(filteredLogs.length / limit) : Math.ceil(total / limit),
    },
  };
};
