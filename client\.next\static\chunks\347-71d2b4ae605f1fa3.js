"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[347],{14636:(e,a,t)=>{t.d(a,{AM:()=>n,Wv:()=>o,hl:()=>i});var s=t(95155);t(12115);var r=t(67140),l=t(59434);function n(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"popover",...a})}function o(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"popover-trigger",...a})}function i(e){let{className:a,align:t="center",sideOffset:n=4,...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.<PERSON>,{"data-slot":"popover-content",align:t,sideOffset:n,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",a),...o})})}},18644:(e,a,t)=>{t.d(a,{HK:()=>r,Ou:()=>u,Pz:()=>l,a1:()=>h,au:()=>m,fm:()=>o,jc:()=>n,kI:()=>c,pO:()=>i,sl:()=>d});var s=t(55077);let r=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await s.S.get("/notifications/classes?page=".concat(e,"&limit=").concat(a))).data.data},l=async()=>(await s.S.get("/notifications/classes/count")).data.data.count,n=async e=>(await s.S.post("/notifications/classes/mark-read/".concat(e))).data,o=async()=>(await s.S.post("/notifications/classes/mark-all-read")).data,i=async()=>(await s.S.delete("/notifications/classes/delete-all")).data,c=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await s.S.get("/notifications/students?page=".concat(e,"&limit=").concat(a))).data.data},d=async()=>(await s.S.get("/notifications/students/count")).data.data.count,u=async e=>(await s.S.post("/notifications/students/mark-read/".concat(e))).data,h=async()=>(await s.S.post("/notifications/students/mark-all-read")).data,m=async()=>(await s.S.delete("/notifications/students/delete-all")).data},21751:(e,a,t)=>{t.d(a,{A$:()=>d,DY:()=>l,Lx:()=>n,RY:()=>o,Ty:()=>i,bi:()=>c,iM:()=>r});var s=t(55077);async function r(e){return(await s.S.post("/auth-client/continue-with-email",e)).data}async function l(e){return(await s.S.post("/auth-client/register",e)).data}async function n(e){return(await s.S.post("/auth-client/login",e)).data}async function o(e){return(await s.S.post("/auth-client/verify-otp",e)).data}async function i(e){return(await s.S.post("/auth-client/resend-otp",e)).data}let c=async(e,a)=>(await s.S.post("/auth-client/generate-jwt",{contact:e,password:a})).data,d=async e=>(await s.S.get("/auth-client/verify-email",{params:{token:e}})).data},30285:(e,a,t)=>{t.d(a,{$:()=>i,r:()=>o});var s=t(95155);t(12115);var r=t(66634),l=t(74466),n=t(59434);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:a,variant:t,size:l,asChild:i=!1,...c}=e,d=i?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:l,className:a})),...c})}},55077:(e,a,t)=>{t.d(a,{S:()=>n});var s=t(23464),r=t(56671);let l=t(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",l);let n=s.A.create({baseURL:l,headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{let a=e.headers["Server-Select"];e.baseURL="uwhizServer"===a?"http://localhost:4006":l;let t=localStorage.getItem("studentToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(r.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},56762:(e,a,t)=>{t.d(a,{A:()=>n,N:()=>l});var s=t(55077),r=t(51990);let l=(0,r.zD)("studentProfile/fetchStudentProfile",async(e,a)=>{let{rejectWithValue:t}=a;try{let e=localStorage.getItem("studentToken");if(!e)return t("No authentication token found");let a=await s.S.get("/student-profile/all-data",{headers:{Authorization:"Bearer ".concat(e)}});if(a.data&&"object"==typeof a.data){if(void 0!==a.data.success&&void 0!==a.data.data)return a.data.data;return a.data}return null}catch(e){var r,l,n;if((null===(r=e.response)||void 0===r?void 0:r.status)===404)return null;return t((null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message)||"Failed to fetch student data")}}),n=(0,r.zD)("studentProfile/updateStudentProfile",async(e,a)=>{let{rejectWithValue:t}=a;try{let a=localStorage.getItem("studentToken");if(!a)return t("No authentication token found");let r=await (0,s.S)({method:"put",url:"/student-profile/combined",data:e,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)}});if(r.data&&"object"==typeof r.data){if(void 0!==r.data.success&&void 0!==r.data.data)return r.data.data;return r.data}return null}catch(e){var r,l;return t((null===(l=e.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message)||"Failed to update student profile")}})},59434:(e,a,t)=>{t.d(a,{MB:()=>o,ZO:()=>n,cn:()=>l,xh:()=>i});var s=t(52596),r=t(39688);function l(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}let n=()=>localStorage.getItem("studentToken"),o=()=>{localStorage.removeItem("studentToken")},i=()=>!!n()},60723:(e,a,t)=>{t.d(a,{RY:()=>i,Ty:()=>c,Xc:()=>d,af:()=>o,bZ:()=>n,iM:()=>r,zy:()=>l});var s=t(55077);let r=async e=>(await s.S.post("/student/continue-with-email",e)).data,l=async e=>(await s.S.post("/student/register",e)).data,n=async e=>(await s.S.post("/student/login",e)).data,o=async()=>{try{let e=await s.S.post("/student/logout");return localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),e.data}catch(e){return localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),{success:!0,message:"Logged out successfully"}}};async function i(e){return(await s.S.post("/student/verify-otp",e)).data}async function c(e){return(await s.S.post("/student/resend-otp",e)).data}let d=async e=>(await s.S.post("/student/verify-email",{token:e})).data},70347:(e,a,t)=>{t.d(a,{default:()=>J});var s=t(95155),r=t(6874),l=t.n(r),n=t(66766),o=t(87949),i=t(55607),c=t(65932),d=t(17576),u=t(13052),h=t(54416),m=t(74783),x=t(81497),f=t(71007),g=t(66516),p=t(79772),v=t(86151),j=t(73783),b=t(30285),N=t(34540);let w=()=>(0,N.wA)();var y=t(12115),k=t(59434),S=t(35695),C=t(92138);let A=()=>{var e;let a=(0,S.useRouter)(),{profileData:t}=(0,N.d4)(e=>e.studentProfile),r=null!==localStorage.getItem("studentToken"),l=(null==t?void 0:null===(e=t.profile)||void 0===e?void 0:e.id)!==void 0;return!r||l?null:(0,s.jsx)("div",{className:"my-4 mx-10 sm:px-4",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900",children:(0,s.jsxs)("div",{className:"px-3 py-1.5 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"bg-[#ff914d] p-1.5 rounded-full",children:(0,s.jsx)(f.A,{className:"h-3 w-3 text-white"})}),(0,s.jsx)("p",{className:"text-xs font-medium text-gray-800 dark:text-gray-200",children:"Please Complete your profile"})]}),(0,s.jsxs)(b.$,{onClick:()=>a.push("/student/profile"),className:"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0",size:"sm",children:["Complete now ",(0,s.jsx)(C.A,{className:"h-3 w-3"})]})]})})})};var z=t(23861),I=t(14636),$=t(90010),R=t(18644),F=t(56671),_=t(75350);function L(e){let{userType:a}=e,[t,r]=(0,y.useState)([]),[l,n]=(0,y.useState)(0),[o,i]=(0,y.useState)(!1),[c,d]=(0,y.useState)(!1),[u,h]=(0,y.useState)(!1),m=(0,S.useRouter)(),x=Array.isArray(t)?t:[],f=(0,y.useCallback)(async()=>{try{let e,t;d(!0),"class"===a?(e=await (0,R.HK)(1,20),t=await (0,R.Pz)()):(e=await (0,R.kI)(1,20),t=await (0,R.sl)());let s=(null==e?void 0:e.notifications)||e||[];r(Array.isArray(s)?s:[]),n(t)}catch(e){console.error("Error fetching notifications:",e),r([]),n(0)}finally{d(!1)}},[a]),g=async e=>{try{var t,s;"class"===a?await (0,R.jc)(e.id):await (0,R.Ou)(e.id),r(a=>a.map(a=>a.id===e.id?{...a,isRead:!0}:a)),n(e=>Math.max(0,e-1)),i(!1),(null===(t=e.data)||void 0===t?void 0:t.actionType)==="OPEN_CHAT"&&(null===(s=e.data)||void 0===s?void 0:s.redirectUrl)&&m.push(e.data.redirectUrl)}catch(e){console.error("Error handling notification click:",e),F.oR.error("Failed to process notification")}},p=async()=>{try{"class"===a?await (0,R.fm)():await (0,R.a1)(),r(e=>e.map(e=>({...e,isRead:!0}))),n(0),F.oR.success("All notifications marked as read")}catch(e){console.error("Error marking all notifications as read:",e),F.oR.error("Failed to mark all notifications as read")}},v=async()=>{h(!1);try{"class"===a?await (0,R.pO)():await (0,R.au)(),r([]),n(0),F.oR.success("All notifications removed successfully")}catch(e){console.error("Error removing all notifications:",e),F.oR.error("Failed to remove all notifications")}};return(0,y.useEffect)(()=>{f();let e=setInterval(f,3e4);return()=>clearInterval(e)},[f]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(I.AM,{open:o,onOpenChange:i,children:[(0,s.jsx)(I.Wv,{asChild:!0,children:(0,s.jsxs)(b.$,{variant:"outline",size:"icon",className:"relative group rounded-full border-2 border-orange-500 hover:border-orange-400 bg-black hover:bg-gray-900 transition-all duration-200 h-8 w-8 md:h-10 md:w-10",children:[(0,s.jsx)("div",{className:"absolute rounded-full inset-0 bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center justify-center",children:[(0,s.jsx)(z.A,{className:"h-4 w-4 md:h-5 md:w-5 text-orange-500 group-hover:text-orange-400 transition-colors duration-200"}),l>0&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 md:-top-2 md:-right-2 h-4 w-4 md:h-5 md:w-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white",children:(0,s.jsx)("span",{className:"text-white text-[10px] md:text-xs font-bold leading-none",children:l>99?"99+":l})})]})]})}),(0,s.jsxs)(I.hl,{className:"w-80 p-0",align:"end",children:[(0,s.jsx)("div",{className:"p-4 border-b",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold",children:"Notifications"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[l>0&&(0,s.jsx)(b.$,{variant:"ghost",size:"sm",onClick:p,className:"text-xs",children:"Mark all read"}),t.length>0&&0===l&&(0,s.jsx)(b.$,{variant:"ghost",size:"sm",onClick:()=>{h(!0)},className:"text-xs text-red-600 hover:text-red-700 hover:bg-red-50",children:"Remove all"})]})]})}),(0,s.jsx)("div",{className:"h-80 overflow-y-auto",children:c?(0,s.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"Loading notifications..."}):0===t.length?(0,s.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"No notifications yet"}):(0,s.jsx)("div",{className:"divide-y",children:Array.isArray(t)&&t.map(e=>(0,s.jsx)("div",{className:"p-4 cursor-pointer hover:bg-muted/50 transition-colors ".concat(e.isRead?"":"bg-blue-50/50"),onClick:()=>g(e),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full mt-2 ".concat(e.isRead?"bg-gray-300":"bg-blue-500")}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-medium text-sm",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:(0,_.m)(new Date(e.createdAt),{addSuffix:!0})})]})]})},e.id))})}),x.length>0&&(0,s.jsx)("div",{className:"p-3 border-t bg-muted/30",children:(0,s.jsx)(b.$,{variant:"ghost",size:"sm",className:"w-full text-xs",onClick:()=>{i(!1),m.push("/notifications")},children:"View All Notifications"})})]})]}),(0,s.jsx)($.Lt,{open:u,onOpenChange:h,children:(0,s.jsxs)($.EO,{children:[(0,s.jsxs)($.wd,{children:[(0,s.jsx)($.r7,{children:"Remove All Notifications"}),(0,s.jsx)($.$v,{children:"Are you sure you want to remove all notifications? This action cannot be undone."})]}),(0,s.jsxs)($.ck,{children:[(0,s.jsx)($.Zr,{children:"Cancel"}),(0,s.jsx)($.Rx,{onClick:v,className:"bg-red-600 hover:bg-red-700",children:"Remove All"})]})]})})]})}var D=t(91394),T=t(60723),P=t(92560),M=t(93457),E=t(56762),U=t(55077),O=t(8619),H=t(14087),B=t(19320),W=t(21751),Z=t(76079);let q=e=>{let{studentId:a}=e,[t,r]=(0,y.useState)(0);return(0,y.useEffect)(()=>{(async()=>{if(!a){r(0);return}let e=await (0,Z.G)(a);e.success&&e.data?r(e.data.streak||0):r(0)})()},[a]),(0,s.jsxs)("span",{className:"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1",children:["\uD83D\uDD25 ",t]})},J=()=>{let{isAuthenticated:e,user:a}=(0,N.d4)(e=>e.user),[t,r]=(0,y.useState)(!1),[C,z]=(0,y.useState)(!1),[$,R]=(0,y.useState)(null),_=w(),Z=(0,S.useRouter)(),J=(0,y.useRef)(null),[Y,V]=(0,y.useState)(0),[X,K]=(0,y.useState)(!1),G=(0,O.d)(0),Q=Y/20;(0,y.useEffect)(()=>{let e=(0,k.xh)();if(z(e),e){let e=localStorage.getItem("student_data");e&&R(JSON.parse(e)),_((0,E.N)())}let a=()=>{let e=(0,k.xh)();if(z(e),e){let e=localStorage.getItem("student_data");e&&R(JSON.parse(e)),_((0,E.N)())}else R(null)};return window.addEventListener("storage",a),J.current&&V(J.current.getBoundingClientRect().width),()=>{window.removeEventListener("storage",a)}},[_]),(0,H.N)((e,a)=>{if(X||0===Y)return;let t=G.get()-Q*a/1e3;t<=-Y&&(t=0),G.set(t)});let ee=()=>r(!t),ea=async()=>{try{let e=await (0,T.af)();!1!==e.success?((0,k.MB)(),z(!1),R(null),localStorage.removeItem("student_data"),_((0,M.Ig)()),F.oR.success("Logged out successfully"),window.dispatchEvent(new Event("storage"))):(F.oR.error(e.message||"Failed to logout"),(0,k.MB)(),z(!1),R(null),localStorage.removeItem("student_data"),_((0,M.Ig)()))}catch(e){console.log("Failed to logout",e),F.oR.error("Failed to logout"),localStorage.removeItem("student_data"),(0,k.MB)(),z(!1),R(null),_((0,M.Ig)())}},et=async()=>{try{let e=await (0,W.bi)(null==a?void 0:a.contactNo,null==a?void 0:a.password);if(e.success){let{token:t}=e.data,s="".concat("http://127.0.0.1:8000","/login-class-link?uid=").concat(null==a?void 0:a.id,"&token=").concat(t);window.location.href=s}else F.oR.error(e.message||"Failed to generate token")}catch(e){console.error("Failed to generate token",e),F.oR.error("Failed to generate token")}},es=[{href:"/verified-classes",label:"Find Tutor",icon:(0,s.jsx)(o.A,{className:"w-4 h-4"})},{href:"/uwhiz",label:"U - Whiz",icon:(0,s.jsx)(i.A,{className:"w-4 h-4"}),isNew:!0},{href:"/mock-exam-card",label:(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:"Daily Quiz"}),C&&(0,s.jsx)(q,{studentId:null==$?void 0:$.id})]}),icon:(0,s.jsx)(c.A,{className:"w-4 h-4"})},{href:"/careers",label:"Career",icon:(0,s.jsx)(d.A,{className:"w-4 h-4"})}],er=(0,s.jsxs)("div",{className:"inline-flex items-center space-x-4 whitespace-nowrap",children:[(0,s.jsx)("span",{className:"text-sm md:text-xl font-semibold text-black",children:"U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion"}),(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition",style:{border:"2px solid black"},onClick:()=>Z.push("/uwhiz-info/".concat(1)),children:["Apply Now ",(0,s.jsx)(u.A,{className:"ml-2 h-4 w-4"})]})]});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("header",{className:"sticky top-0 z-50 w-full bg-black overflow-x-hidden",children:[(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex h-20 items-center justify-between",children:[(0,s.jsx)(l(),{href:"/",className:"flex items-center space-x-2 transition-transform hover:scale-105",children:(0,s.jsx)(n.default,{src:"/logo_black.png",alt:"Preply Logo",width:150,height:50,className:"rounded-sm"})}),(0,s.jsx)("nav",{className:"hidden md:flex items-center space-x-4",children:es.map(e=>(0,s.jsxs)(l(),{href:e.href,className:"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400",children:[e.icon,e.label,"Find School"===e.label&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black",children:"Coming Soon"}),e.isNew&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse",children:"Trending"})]},e.href))}),(0,s.jsxs)("div",{className:"flex md:hidden items-center space-x-2",children:[e&&(0,s.jsx)(L,{userType:"class"}),C&&(0,s.jsx)(L,{userType:"student"}),(0,s.jsx)(b.$,{variant:"ghost",size:"icon",className:"text-orange-400 hover:bg-orange-500/10",onClick:ee,children:t?(0,s.jsx)(h.A,{className:"h-6 w-6"}):(0,s.jsx)(m.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(L,{userType:"class"}),(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(l(),{href:"/coins",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",size:"icon",className:"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500",children:[(0,s.jsx)("div",{className:"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,s.jsx)("div",{className:"relative z-10 ",children:(0,s.jsx)(n.default,{src:"/uest_coin.png",alt:"Coin Icon",width:32,height:32,className:"object-contain"})})]})})}),(0,s.jsx)(l(),{href:"/classes/chat",passHref:!0,children:(0,s.jsx)(b.$,{variant:"outline",size:"icon",className:"relative rounded-full border-2 border-orange-500 group bg-black text-white hover:text-orange-400 h-10 w-10",children:(0,s.jsx)(x.A,{className:"h-5 w-5"})})})]}),(0,s.jsx)("div",{className:"h-8 border-l border-orange-500/20"}),e&&(0,s.jsxs)(I.AM,{children:[(0,s.jsx)(I.Wv,{asChild:!0,children:(0,s.jsx)(D.eu,{className:"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10",children:(0,s.jsx)(D.q5,{className:"bg-white text-black flex items-center justify-center text-sm font-semibold",children:(null==a?void 0:a.firstName)&&(null==a?void 0:a.lastName)?"".concat(a.firstName[0]).concat(a.lastName[0]).toUpperCase():"CT"})})}),(0,s.jsxs)(I.hl,{className:"w-64 bg-white",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-3 pb-2 border-b",children:[(0,s.jsx)(D.eu,{className:"h-12 w-12 border-2 border-[#ff914d]",children:(0,s.jsx)(D.q5,{className:"bg-white text-black",children:(null==a?void 0:a.firstName)&&(null==a?void 0:a.lastName)?"".concat(a.firstName[0]).concat(a.lastName[0]).toUpperCase():"CT"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-black",children:(null==a?void 0:a.firstName)&&(null==a?void 0:a.lastName)?"".concat(a.firstName," ").concat(a.lastName):(null==a?void 0:a.className)||"Class Account"}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:(null==a?void 0:a.contactNo)||"<EMAIL>"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,s.jsxs)(l(),{href:"/classes/profile",className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]})}),(0,s.jsxs)(b.$,{onClick:()=>et(),className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"My Dashboard"})]}),(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,s.jsxs)(l(),{href:"/classes/referral-dashboard",className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Referral Dashboard"})]})}),(0,s.jsx)(b.$,{variant:"outline",className:"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10",onClick:async()=>{try{(await U.S.post("/auth-client/logout",{})).data.success&&(Z.push("/"),_((0,P.lM)()),localStorage.removeItem("token"),F.oR.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),F.oR.error("Failed to logout")}},children:"Logout"})]})]})]}),!e&&!C&&(0,s.jsx)(b.$,{className:"bg-customOrange hover:bg-[#E88143] text-white mr-4",asChild:!0,children:(0,s.jsx)(l(),{href:"/class/login",children:"Join as a Tutor/Class"})}),!e&&!C&&(0,s.jsx)(b.$,{variant:"outline",className:"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white",asChild:!0,children:(0,s.jsx)(l(),{href:"/student/login",children:"Student Login"})}),C&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(L,{userType:"student"}),(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(l(),{href:"/coins",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",size:"icon",className:"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500",children:[(0,s.jsx)("div",{className:"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,s.jsx)("div",{className:"relative z-10 ",children:(0,s.jsx)(n.default,{src:"/uest_coin.png",alt:"Coin Icon",width:32,height:32,className:"object-contain"})})]})})}),(0,s.jsx)(l(),{href:"/student/chat",passHref:!0,children:(0,s.jsx)(b.$,{variant:"outline",size:"icon",className:"relative rounded-full group border-2 border-orange-500 bg-black text-white hover:text-orange-400 h-10 w-10",children:(0,s.jsx)(x.A,{className:"h-5 w-5"})})})]}),C&&(0,s.jsxs)(I.AM,{children:[(0,s.jsx)(I.Wv,{asChild:!0,children:(0,s.jsx)(D.eu,{className:"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10",children:(0,s.jsx)(D.q5,{className:"bg-white text-black flex items-center justify-center text-sm font-semibold",children:(null==$?void 0:$.firstName)&&(null==$?void 0:$.lastName)?"".concat($.firstName[0]).concat($.lastName[0]).toUpperCase():"ST"})})}),(0,s.jsxs)(I.hl,{className:"w-64 bg-white",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-3 pb-2 border-b",children:[(0,s.jsx)(D.eu,{className:"h-12 w-12 border-2 border-[#ff914d]",children:(0,s.jsx)(D.q5,{className:"bg-white text-black",children:(null==$?void 0:$.firstName)&&(null==$?void 0:$.lastName)?"".concat($.firstName[0]).concat($.lastName[0]).toUpperCase():"ST"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-black",children:(null==$?void 0:$.firstName)&&(null==$?void 0:$.lastName)?"".concat($.firstName," ").concat($.lastName):"Student Account"}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:(null==$?void 0:$.contactNo)||"<EMAIL>"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,s.jsxs)(l(),{href:"/student/profile",className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]})}),(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,s.jsxs)(l(),{href:"/student/wishlist",className:"flex items-center",children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"My Wishlist"})]})}),(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,s.jsxs)(l(),{href:"/student/referral-dashboard",className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Referral Dashboard"})]})}),(0,s.jsx)(b.$,{variant:"outline",className:"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10",onClick:ea,children:"Logout"})]})]})]})]})]})}),(0,s.jsxs)("div",{className:"w-screen bg-[#FD904B] border-y border-black relative mt-1",children:[(0,s.jsx)("div",{className:"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0"}),(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden",children:(0,s.jsxs)(B.P.div,{className:"inline-flex py-2 px-4",style:{x:G},onMouseEnter:()=>K(!0),onMouseLeave:()=>K(!1),children:[(0,s.jsx)("div",{ref:J,className:"inline-flex items-center space-x-4 whitespace-nowrap pr-8",children:er}),(0,s.jsx)("div",{className:"inline-flex items-center space-x-4 whitespace-nowrap pr-8",children:er})]})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ".concat(t?"translate-x-0":"translate-x-full"),children:(0,s.jsxs)("div",{className:"flex flex-col h-full p-6",children:[(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(b.$,{variant:"ghost",size:"icon",className:"text-orange-400 hover:bg-orange-500/10 rounded-full",onClick:ee,children:(0,s.jsx)(h.A,{className:"h-6 w-6"})})}),(0,s.jsx)("nav",{className:"flex flex-col space-y-2 mt-8",children:es.map(e=>(0,s.jsxs)(l(),{href:e.href,className:"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center",onClick:ee,children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon,"string"==typeof e.label?(0,s.jsx)("span",{children:e.label}):e.label]}),"Find School"===e.label&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse",children:"Coming Soon"}),e.isNew&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white",children:"New"})]},e.href))}),(0,s.jsxs)("div",{className:"mt-auto space-y-4",children:[e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l(),{href:"/classes/profile",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full",children:(0,s.jsx)(f.A,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"font-medium text-gray-300",children:"Profile"})]})]})}),(0,s.jsxs)(b.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:()=>et(),children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full",children:(0,s.jsx)(j.A,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"font-medium text-gray-300",children:"My Dashboard"})]})]}),(0,s.jsx)(l(),{href:"/classes/referral-dashboard",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full",children:(0,s.jsx)(g.A,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"font-medium text-gray-300",children:"Referral Dashboard"})]})]})}),(0,s.jsx)(l(),{href:"/coins",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full",children:(0,s.jsx)(n.default,{src:"/uest_coin.png",alt:"Coin Icon",width:20,height:20,className:"object-contain"})}),(0,s.jsx)("span",{className:"font-medium text-gray-300",children:"My Coins"})]})]})}),(0,s.jsx)(l(),{href:"/classes/chat",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"font-medium text-gray-300",children:"Chat"})]})]})}),(0,s.jsx)(b.$,{variant:"outline",className:"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 hover:text-white mt-3",onClick:async()=>{try{(await U.S.post("/auth-client/logout",{})).data.success&&(Z.push("/"),_((0,P.lM)()),localStorage.removeItem("token"),F.oR.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),F.oR.error("Failed to logout")}ee()},children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Logout"})]})})]}),C&&(0,s.jsxs)(s.Fragment,{children:[(null==$?void 0:$.firstName)&&(null==$?void 0:$.lastName)&&(0,s.jsx)("div",{className:"p-3 border border-[#ff914d]/20 rounded-lg bg-white",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(D.eu,{className:"h-12 w-12 border-2 border-[#ff914d]",children:(0,s.jsx)(D.q5,{className:"bg-white text-black",children:"".concat($.firstName[0]).concat($.lastName[0]).toUpperCase()})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-black",children:"".concat($.firstName," ").concat($.lastName)}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:$.email})]})]})}),(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",onClick:ee,children:(0,s.jsxs)(l(),{href:"/student/profile",className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(p.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Profile"})]})}),(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",onClick:ee,children:(0,s.jsxs)(l(),{href:"/student/wishlist",className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(v.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"My Wishlist"})]})}),(0,s.jsx)(b.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",onClick:ee,children:(0,s.jsxs)(l(),{href:"/student/referral-dashboard",className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(g.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Referral Dashboard"})]})}),(0,s.jsx)(l(),{href:"/coins",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",className:"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3",onClick:ee,children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center justify-center gap-3",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500",children:(0,s.jsx)(n.default,{src:"/uest_coin.png",alt:"Coin Icon",width:20,height:20,className:"object-contain"})}),(0,s.jsx)("span",{className:"font-medium text-gray-300",children:"My Coins"})]})]})}),(0,s.jsx)(l(),{href:"/student/chat",passHref:!0,children:(0,s.jsxs)(b.$,{variant:"outline",className:"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3",onClick:ee,children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center justify-center gap-3",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"font-medium text-gray-300",children:"Chat"})]})]})}),(0,s.jsx)(b.$,{variant:"outline",className:"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10",onClick:()=>{ea(),ee()},children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Logout"})]})})]}),!e&&!C&&(0,s.jsxs)("div",{className:"space-y-3 pt-3",children:[(0,s.jsx)(b.$,{variant:"default",className:"w-full bg-orange-500 hover:bg-orange-600",asChild:!0,children:(0,s.jsx)(l(),{href:"/class/login",onClick:ee,children:"Tutor/Classes Login"})}),(0,s.jsx)(b.$,{variant:"outline",className:"w-full border-customOrange text-orange-500 hover:bg-orange",asChild:!0,children:(0,s.jsx)(l(),{href:"/student/login",onClick:ee,children:"Student Login"})})]})]})]})}),C&&(0,s.jsx)(A,{})]})]})}},76079:(e,a,t)=>{t.d(a,{$:()=>r,G:()=>l});var s=t(55077);let r=async e=>{try{let a=await s.S.put("/mock-exam-streak/".concat(e),{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:a.data.data}}catch(e){var a,t;return{success:!1,error:"Failed to save mock exam streak: ".concat((null===(t=e.response)||void 0===t?void 0:null===(a=t.data)||void 0===a?void 0:a.error)||e.message)}}},l=async e=>{try{let a=await s.S.get("/mock-exam-streak/".concat(e),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:a.data.data}}catch(e){var a,t;return{success:!1,error:"Failed to get mock exam streak: ".concat((null===(t=e.response)||void 0===t?void 0:null===(a=t.data)||void 0===a?void 0:a.error)||e.message)}}}},90010:(e,a,t)=>{t.d(a,{$v:()=>f,EO:()=>u,Lt:()=>o,Rx:()=>g,Zr:()=>p,ck:()=>m,r7:()=>x,tv:()=>i,wd:()=>h});var s=t(95155);t(12115);var r=t(35563),l=t(59434),n=t(30285);function o(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"alert-dialog",...a})}function i(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"alert-dialog-trigger",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,s.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function u(e){let{className:a,...t}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{}),(0,s.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...t})]})}function h(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function m(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function x(e){let{className:a,...t}=e;return(0,s.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",a),...t})}function f(e){let{className:a,...t}=e;return(0,s.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...t})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(r.rc,{className:(0,l.cn)((0,n.r)(),a),...t})}function p(e){let{className:a,...t}=e;return(0,s.jsx)(r.ZD,{className:(0,l.cn)((0,n.r)({variant:"outline"}),a),...t})}},91394:(e,a,t)=>{t.d(a,{eu:()=>n,q5:()=>o});var s=t(95155);t(12115);var r=t(87083),l=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",a),...t})}},92560:(e,a,t)=>{t.d(a,{Ay:()=>c,gV:()=>o,lM:()=>i});var s=t(51990);let r=localStorage.getItem("user"),l={user:r?JSON.parse(r):null,isAuthenticated:!!r},n=(0,s.Z0)({name:"user",initialState:l,reducers:{setUser:(e,a)=>{e.user=a.payload.user,e.isAuthenticated=!0,localStorage.setItem("user",JSON.stringify(a.payload.user))},clearUser:e=>{e.user=null,e.isAuthenticated=!1,localStorage.removeItem("user")}}}),{setUser:o,clearUser:i}=n.actions,c=n.reducer},93457:(e,a,t)=>{t.d(a,{Ay:()=>c,Ig:()=>i,XY:()=>o});var s=t(51990),r=t(56762);let l=(0,s.Z0)({name:"studentProfile",initialState:{profileData:null,loading:!1,error:null},reducers:{setStudentProfileData(e,a){var t,s;if(e.profileData=a.payload,null===(s=a.payload)||void 0===s?void 0:null===(t=s.profile)||void 0===t?void 0:t.photo)try{localStorage.setItem("student_profile_photo",a.payload.profile.photo)}catch(e){console.error("Failed to persist photo to localStorage:",e)}},updateProfilePhoto(e,a){var t;if(null===(t=e.profileData)||void 0===t?void 0:t.profile){e.profileData.profile.photo=a.payload;try{a.payload?localStorage.setItem("student_profile_photo",a.payload):localStorage.removeItem("student_profile_photo")}catch(e){console.error("Failed to persist photo to localStorage:",e)}}},clearStudentProfileData(e){e.profileData=null,e.loading=!1,e.error=null;try{localStorage.removeItem("student_profile_photo")}catch(e){console.error("Failed to clear photo from localStorage:",e)}}},extraReducers:e=>{e.addCase(r.N.pending,e=>{e.loading=!0,e.error=null}).addCase(r.N.fulfilled,(e,a)=>{e.loading=!1,a.payload&&(e.profileData=a.payload)}).addCase(r.N.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(r.A.pending,e=>{e.loading=!0,e.error=null}).addCase(r.A.fulfilled,(e,a)=>{e.loading=!1,a.payload&&(e.profileData=a.payload)}).addCase(r.A.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{setStudentProfileData:n,updateProfilePhoto:o,clearStudentProfileData:i}=l.actions,c=l.reducer}}]);