'use client';

import { useState, useEffect, useCallback } from 'react';
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/app-components/dataTable";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { getActivityLogs } from '@/services/activityLogApi';
import { toast } from 'sonner';
import Pagination from "@/app-components/pagination";
import { Input } from '@/components/ui/input';
import { ActivityLog } from '@/lib/types';
import { Mail, Phone, Filter, Search } from 'lucide-react';

const PAGE_SIZE = 10;

const ActivityLogsPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [userTypeFilter, setUserTypeFilter] = useState<"ALL" | "STUDENT" | "CLASS">("ALL");
  const [activityTypeFilter, setActivityTypeFilter] = useState<string>("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchName, setSearchName] = useState('');
  const [searchEmail, setSearchEmail] = useState('');
  const [searchContact, setSearchContact] = useState('');

  const fetchActivityLogs = useCallback(
    async (page: number, userType?: "STUDENT" | "CLASS", activityType?: string, searchParams?: {
      name?: string;
      email?: string;
      contact?: string;
    }) => {
      try {
        setIsLoading(true);
        const params = {
          page,
          limit: PAGE_SIZE,
          ...(userType && { userType }),
          ...(activityType && { activityType }),
          ...(searchParams?.name && { searchName: searchParams.name }),
          ...(searchParams?.email && { searchEmail: searchParams.email }),
          ...(searchParams?.contact && { searchContact: searchParams.contact }),
        };

        const response = await getActivityLogs(params);
        setLogs(response.logs);
        setTotalPages(response.pagination.totalPages || 1);
      } catch (error: any) {
        toast.error(error.message || "Failed to fetch activity logs");
        setLogs([]);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    fetchActivityLogs(1);
  }, [fetchActivityLogs]);

  const handleFilter = () => {
    const userType = userTypeFilter !== "ALL" ? userTypeFilter : undefined;
    const activityType = activityTypeFilter !== "ALL" ? activityTypeFilter : undefined;
    const searchParams = {
      name: searchName.trim() || undefined,
      email: searchEmail.trim() || undefined,
      contact: searchContact.trim() || undefined,
    };
    const hasSearchParams = searchParams.name || searchParams.email || searchParams.contact;
    setCurrentPage(1);
    fetchActivityLogs(1, userType, activityType, hasSearchParams ? searchParams : undefined);
  };

  const handleReset = () => {
    setSearchName('');
    setSearchEmail('');
    setSearchContact('');
    setUserTypeFilter('ALL');
    setActivityTypeFilter('ALL');
    setCurrentPage(1);
    fetchActivityLogs(1);
  };

  const getActivityBadgeColor = (activity: string) => {
    switch (activity) {
      case 'LOGIN':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'LOGOUT':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'REGISTRATION':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUserTypeBadgeColor = (userType: string) => {
    switch (userType) {
      case 'STUDENT':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'CLASS':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const columns: ColumnDef<ActivityLog>[] = [
    {
      accessorKey: "userDetails.firstName",
      header: "User Name",
      cell: ({ row }) => {
        const userDetails = row.original.userDetails;
        return userDetails ? `${userDetails.firstName} ${userDetails.lastName}` : "Unknown User";
      },
    },
    {
      accessorKey: "userType",
      header: "User Type",
      cell: ({ row }) => (
        <Badge className={getUserTypeBadgeColor(row.original.userType)}>
          {row.original.userType}
        </Badge>
      ),
    },
    {
      accessorKey: "activityType",
      header: "Activity",
      cell: ({ row }) => (
        <Badge className={getActivityBadgeColor(row.original.activityType)}>
          {row.original.activityType}
        </Badge>
      ),
    },
    {
      accessorKey: "userDetails.email",
      header: "Email",
      cell: ({ row }) => row.original.userDetails?.email || "N/A",
    },
    {
      accessorKey: "userDetails.contact",
      header: "Contact",
      cell: ({ row }) => {
        const userDetails = row.original.userDetails;
        if (!userDetails) return "N/A";
        return userDetails.contact || userDetails.contactNo || "N/A";
      },
    },
    {
      accessorKey: "createdAt",
      header: "Date & Time",
      cell: ({ row }) => format(new Date(row.original.createdAt), 'PPp'),
    },
  ];

  return (
    <div className="p-4">
      <div className="mx-2 mb-6">
        <h1 className="text-2xl font-bold ms-2 mb-6">User Activity Logs</h1>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by name..."
                      value={searchName}
                      onChange={(e) => setSearchName(e.target.value)}
                      onKeyDown={(e) => e.key === "Enter" && handleFilter()}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by email..."
                      value={searchEmail}
                      onChange={(e) => setSearchEmail(e.target.value)}
                      onKeyDown={(e) => e.key === "Enter" && handleFilter()}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by contact..."
                      value={searchContact}
                      onChange={(e) => setSearchContact(e.target.value)}
                      onKeyDown={(e) => e.key === "Enter" && handleFilter()}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
              <div className="flex flex-col md:flex-row gap-4">
                <Select
                  value={userTypeFilter}
                  onValueChange={(value: "ALL" | "STUDENT" | "CLASS") => setUserTypeFilter(value)}
                >
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Filter by user type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Types</SelectItem>
                    <SelectItem value="STUDENT">Students</SelectItem>
                    <SelectItem value="CLASS">Classes</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={activityTypeFilter}
                  onValueChange={(value: string) => setActivityTypeFilter(value)}
                >
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Filter by activity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Activities</SelectItem>
                    <SelectItem value="LOGIN">Login</SelectItem>
                    <SelectItem value="LOGOUT">Logout</SelectItem>
                    <SelectItem value="REGISTRATION">Registration</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2">
                <Button onClick={handleFilter} className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Apply Filters
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="flex items-center gap-2"
                >
                  Clear All
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Logs Cards */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-center text-muted-foreground">
              Loading activity logs...
            </div>
          </div>
        ) : logs.length === 0 ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-center text-muted-foreground">
              No activity logs found
            </div>
          </div>
        ) : (
          <div className="grid gap-4">
            {logs.map((log) => (
              <Card key={log.id} className="hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    {/* Activity Icon */}
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(log.activityType)}
                    </div>

                    {/* Main Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                          {/* User Name and Type */}
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-lg">
                              {log.userDetails
                                ? `${log.userDetails.firstName} ${log.userDetails.lastName}`
                                : "Unknown User"
                              }
                            </h3>
                            <Badge className={getUserTypeBadgeColor(log.userType)}>
                              {log.userType}
                            </Badge>
                            <Badge className={getActivityBadgeColor(log.activityType)}>
                              {log.activityType}
                            </Badge>
                          </div>

                          {/* User Details */}
                          <div className="space-y-1 text-sm text-muted-foreground">
                            {log.userDetails?.email && (
                              <div className="flex items-center gap-2">
                                <Mail className="w-4 h-4" />
                                <span>{log.userDetails.email}</span>
                              </div>
                            )}
                            {(log.userDetails?.contact || log.userDetails?.contactNo) && (
                              <div className="flex items-center gap-2">
                                <Phone className="w-4 h-4" />
                                <span>{log.userDetails.contact || log.userDetails.contactNo}</span>
                              </div>
                            )}
                            {log.userDetails?.className && (
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4" />
                                <span>Class: {log.userDetails.className}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Timestamp */}
                        <div className="text-right text-sm text-muted-foreground">
                          <div className="flex items-center gap-1 mb-1">
                            <Clock className="w-4 h-4" />
                            <span>{formatDistanceToNow(new Date(log.createdAt), { addSuffix: true })}</span>
                          </div>
                          <div className="text-xs">
                            {format(new Date(log.createdAt), 'PPp')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          const userType = userTypeFilter !== "ALL" ? userTypeFilter : undefined;
          const activityType = activityTypeFilter !== "ALL" ? activityTypeFilter : undefined;
          const searchParams = {
            name: searchName.trim() || undefined,
            email: searchEmail.trim() || undefined,
            contact: searchContact.trim() || undefined,
          };
          const hasSearchParams = searchParams.name || searchParams.email || searchParams.contact;
          fetchActivityLogs(page, userType, activityType, hasSearchParams ? searchParams : undefined);
        }}
        entriesText={`${logs.length} entries`}
      />
    </div>
  );
};

export default ActivityLogsPage;
